<template>
  <div class="map-example">
    <h2>{{ $T('百度地图选点组件示例') }}</h2>
    
    <div class="example-section">
      <el-button type="primary" @click="openMapDialog">
        {{ $T('打开地图选点') }}
      </el-button>
      
      <div v-if="selectedLocation" class="selected-result">
        <h3>{{ $T('选中的位置信息') }}:</h3>
        <p><strong>{{ $T('经度') }}:</strong> {{ selectedLocation.lng }}</p>
        <p><strong>{{ $T('纬度') }}:</strong> {{ selectedLocation.lat }}</p>
        <p><strong>{{ $T('地址') }}:</strong> {{ selectedLocation.address }}</p>
      </div>
    </div>

    <!-- 地图选点弹窗 -->
    <MapPointDialog
      v-model="showMapDialog"
      :default-point="defaultMapPoint"
      @confirm="handleLocationConfirm"
    />
  </div>
</template>

<script>
import MapPointDialog from './MapPointDialog.vue';

export default {
  name: 'MapPointDialogExample',
  components: {
    MapPointDialog
  },
  data() {
    return {
      showMapDialog: false,
      selectedLocation: null,
      // 默认地图中心点（北京天安门）
      defaultMapPoint: {
        lng: 116.404,
        lat: 39.915
      }
    };
  },
  methods: {
    /**
     * 打开地图选点弹窗
     */
    openMapDialog() {
      this.showMapDialog = true;
    },

    /**
     * 处理位置确认
     */
    handleLocationConfirm(location) {
      this.selectedLocation = location;
      this.$message.success(this.$T('位置选择成功'));
      console.log('选中的位置:', location);
    }
  }
};
</script>

<style lang="scss" scoped>
.map-example {
  padding: 20px;
  
  .example-section {
    margin-top: 20px;
    
    .selected-result {
      margin-top: 20px;
      padding: 16px;
      background-color: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
      
      h3 {
        margin: 0 0 12px 0;
        color: #1f2937;
      }
      
      p {
        margin: 8px 0;
        font-size: 14px;
        
        strong {
          color: #374151;
        }
      }
    }
  }
}
</style>
